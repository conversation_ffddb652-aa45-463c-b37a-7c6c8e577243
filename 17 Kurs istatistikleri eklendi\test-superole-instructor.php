<?php
/**
 * Superole Kullanıcısının Tutor LMS Eğitmen Durumunu Test Et
 * 
 * Bu dosyayı WordPress root dizininde çalıştırarak mevcut superole kullanıcısının
 * Tutor LMS eğitmen durumunu test edebilirsiniz.
 * 
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php test-superole-instructor.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Superole Kullanıcısının Tutor LMS Eğitmen Durumu Testi ===\n\n";

// 1. Tutor LMS aktif mi kontrol et
if (!class_exists('TUTOR\Tutor') || !function_exists('tutor_time')) {
    die("✗ Tutor LMS bulunamadı! Önce Tutor LMS eklentisini etkinleştirin.\n");
}

echo "✓ Tutor LMS aktif.\n";

// 2. Superole kullanıcılarını al
$superole_users = get_users(['role' => 'superole']);

if (empty($superole_users)) {
    echo "ℹ️  Henüz hiçbir kullanıcı superole rolüne sahip değil.\n";
    exit(0);
}

echo "📊 Toplam " . count($superole_users) . " superole kullanıcısı bulundu.\n\n";

// 3. Her kullanıcı için detaylı test
foreach ($superole_users as $user) {
    echo "👤 Kullanıcı: {$user->display_name} ({$user->user_login}) - ID: {$user->ID}\n";
    echo "   Roller: " . implode(', ', $user->roles) . "\n";
    
    // Meta verileri kontrol et
    $is_instructor = get_user_meta($user->ID, '_is_tutor_instructor', true);
    $instructor_status = get_user_meta($user->ID, '_tutor_instructor_status', true);
    $instructor_approved = get_user_meta($user->ID, '_tutor_instructor_approved', true);
    
    echo "\n   📋 Meta Veriler:\n";
    echo "   - _is_tutor_instructor: " . ($is_instructor ? "✓ Var (" . date('Y-m-d H:i:s', $is_instructor) . ")" : "✗ YOK!") . "\n";
    echo "   - _tutor_instructor_status: " . ($instructor_status ? "✓ {$instructor_status}" : "✗ Yok") . "\n";
    echo "   - _tutor_instructor_approved: " . ($instructor_approved ? "✓ Var (" . date('Y-m-d H:i:s', $instructor_approved) . ")" : "✗ Yok") . "\n";
    
    // Tutor instructor rolü kontrol et
    $has_tutor_role = in_array('tutor_instructor', $user->roles);
    echo "   - tutor_instructor rolü: " . ($has_tutor_role ? "✓ Var" : "✗ YOK!") . "\n";
    
    // Tutor LMS fonksiyonlarını test et
    echo "\n   🧪 Tutor LMS Fonksiyon Testleri:\n";
    
    // User::is_instructor() testi
    if (class_exists('TUTOR\User')) {
        $is_instructor_func = TUTOR\User::is_instructor($user->ID);
        echo "   - User::is_instructor(): " . ($is_instructor_func ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // tutor_utils()->is_instructor() testi
    if (function_exists('tutor_utils')) {
        $is_instructor_utils = tutor_utils()->is_instructor($user->ID);
        echo "   - tutor_utils()->is_instructor(): " . ($is_instructor_utils ? "✓ TRUE" : "✗ FALSE") . "\n";
    }
    
    // current_user_can() simülasyonu
    wp_set_current_user($user->ID);
    $can_instructor = current_user_can('tutor_instructor');
    echo "   - current_user_can('tutor_instructor'): " . ($can_instructor ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Course oluşturma yetki kontrolü simülasyonu
    $has_access = false;
    if (class_exists('TUTOR\User')) {
        $has_access = TUTOR\User::is_admin($user->ID) || TUTOR\User::is_instructor($user->ID);
    }
    echo "   - Course AJAX Access (User::is_admin() || User::is_instructor()): " . ($has_access ? "✓ TRUE" : "✗ FALSE") . "\n";
    
    // Capabilities kontrol et
    echo "\n   🔑 Önemli Capabilities:\n";
    $important_caps = [
        'edit_tutor_course',
        'publish_tutor_course', 
        'delete_tutor_course',
        'tutor_instructor',
        'manage_tutor_instructor'
    ];
    
    foreach ($important_caps as $cap) {
        $has_cap = $user->has_cap($cap);
        echo "   - {$cap}: " . ($has_cap ? "✓" : "✗") . "\n";
    }
    
    // Sorun tespiti
    echo "\n   🔍 Sorun Analizi:\n";
    if (!$is_instructor) {
        echo "   ❌ SORUN: _is_tutor_instructor meta verisi eksik!\n";
        echo "   💡 Çözüm: fix-superole-instructor.php dosyasını çalıştırın.\n";
    }
    
    if (!$has_tutor_role) {
        echo "   ❌ SORUN: tutor_instructor rolü eksik!\n";
        echo "   💡 Çözüm: fix-superole-instructor.php dosyasını çalıştırın.\n";
    }
    
    if (!$has_access) {
        echo "   ❌ SORUN: Kurs oluşturma yetkisi yok!\n";
        echo "   💡 Çözüm: Yukarıdaki sorunları çözün.\n";
    }
    
    if ($is_instructor && $has_tutor_role && $has_access) {
        echo "   ✅ HER ŞEY TAMAM: Bu kullanıcı kurs oluşturabilir!\n";
    }
    
    echo "\n" . str_repeat("-", 80) . "\n\n";
}

// Genel öneriler
echo "=== GENEL ÖNERİLER ===\n";
echo "1. Eğer meta veriler eksikse: fix-superole-instructor.php dosyasını çalıştırın\n";
echo "2. Eğer roller eksikse: Role Custom eklentisini devre dışı bırakıp tekrar etkinleştirin\n";
echo "3. Sorun devam ederse: WordPress cache'ini temizleyin\n";
echo "4. Hala çalışmıyorsa: Tutor LMS ayarlarını kontrol edin\n";

echo "\n=== Test Tamamlandı ===\n";

// Current user'ı sıfırla
wp_set_current_user(0);
?>
