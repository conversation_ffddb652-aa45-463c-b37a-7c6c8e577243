/* Role Custom Reports CSS */

.role-custom-reports {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.role-custom-reports h1 {
    color: #23282d;
    font-size: 28px;
    margin-bottom: 30px;
    font-weight: 600;
}

/* <PERSON><PERSON><PERSON> */
.role-custom-summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

.summary-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    display: flex;
    align-items: center;
    gap: 16px;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.summary-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.summary-card .card-icon .dashicons {
    color: #fff;
    font-size: 24px;
    width: 24px;
    height: 24px;
}

.summary-card .card-content h3 {
    font-size: 32px;
    font-weight: 700;
    color: #23282d;
    margin: 0 0 4px 0;
    line-height: 1;
}

.summary-card .card-content p {
    font-size: 14px;
    color: #666;
    margin: 0;
    font-weight: 500;
}

/* Grafik Konteyneri */
.role-custom-charts-container {
    display: grid;
    gap: 30px;
}

.chart-section {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 8px;
    padding: 24px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.chart-header h2 {
    font-size: 20px;
    color: #23282d;
    margin: 0;
    font-weight: 600;
}

.chart-controls select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
    color: #555;
    cursor: pointer;
    transition: border-color 0.2s ease;
}

.chart-controls select:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

/* Özel Tarih Aralığı */
.custom-date-range {
    margin-top: 12px;
    padding: 16px;
    background: #f9f9f9;
    border: 1px solid #e1e1e1;
    border-radius: 6px;
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.custom-date-range input[type="date"] {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
    font-size: 14px;
    color: #555;
    min-width: 140px;
}

.custom-date-range input[type="date"]:focus {
    outline: none;
    border-color: #0073aa;
    box-shadow: 0 0 0 1px #0073aa;
}

.custom-date-range span {
    color: #666;
    font-weight: 500;
    font-size: 14px;
}

.custom-date-range .button {
    padding: 8px 16px;
    font-size: 14px;
    height: auto;
    line-height: 1.4;
}

.chart-container {
    position: relative;
    height: 400px;
    margin-top: 20px;
}

.chart-container canvas {
    max-height: 400px;
}



/* Responsive Design */
@media (max-width: 768px) {
    .role-custom-summary-cards {
        grid-template-columns: 1fr;
    }

    .chart-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .chart-container {
        height: 300px;
    }

    .summary-card {
        padding: 20px;
    }

    .summary-card .card-content h3 {
        font-size: 28px;
    }

    .custom-date-range {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .custom-date-range input[type="date"] {
        min-width: auto;
        width: 100%;
    }
}

@media (max-width: 480px) {
    .role-custom-reports {
        padding: 15px;
    }
    
    .summary-card {
        padding: 16px;
        flex-direction: column;
        text-align: center;
    }
    
    .summary-card .card-icon {
        width: 50px;
        height: 50px;
    }
    
    .summary-card .card-icon .dashicons {
        font-size: 20px;
        width: 20px;
        height: 20px;
    }
    
    .chart-container {
        height: 250px;
    }
}

/* Özel Renkler */
.summary-card:nth-child(1) .card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.summary-card:nth-child(2) .card-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.summary-card:nth-child(3) .card-icon {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.summary-card:nth-child(4) .card-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.summary-card:nth-child(5) .card-icon {
    background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.summary-card:nth-child(6) .card-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.summary-card:nth-child(4) .card-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

/* Chart Tooltip Özelleştirme */
.chartjs-tooltip {
    background: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    color: #fff;
    font-size: 12px;
    padding: 8px 12px;
    pointer-events: none;
    position: absolute;
    transform: translate(-50%, -100%);
}

/* Hata Mesajları */
.role-custom-error {
    background: #fff;
    border-left: 4px solid #dc3232;
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-custom-error p {
    margin: 0;
    color: #dc3232;
    font-weight: 500;
}

/* Başarı Mesajları */
.role-custom-success {
    background: #fff;
    border-left: 4px solid #46b450;
    padding: 16px 20px;
    margin: 20px 0;
    border-radius: 0 4px 4px 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.role-custom-success p {
    margin: 0;
    color: #46b450;
    font-weight: 500;
}

/* Boş Durum */
.role-custom-empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #666;
}

.role-custom-empty-state .dashicons {
    font-size: 64px;
    color: #ddd;
    margin-bottom: 20px;
}

.role-custom-empty-state h3 {
    font-size: 24px;
    color: #555;
    margin-bottom: 12px;
}

.role-custom-empty-state p {
    font-size: 16px;
    color: #777;
    max-width: 400px;
    margin: 0 auto;
    line-height: 1.5;
}
