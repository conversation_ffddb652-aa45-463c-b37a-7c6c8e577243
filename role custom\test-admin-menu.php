<?php
/**
 * Role Custom Test Script
 * Bu dosya eklentinin admin menü özelliklerini test etmek için kull<PERSON>lı<PERSON>
 * 
 * KULLANIM:
 * 1. Bu dosyayı WordPress root dizinine kopyala<PERSON>ın
 * 2. Tarayıcıda yoursite.com/test-admin-menu.php adresini ziyaret edin
 * 3. Test sonuçlarını kontrol edin
 * 
 * NOT: Bu dosyayı test sonrası silin!
 */

// WordPress'i yükle
require_once('wp-config.php');
require_once('wp-load.php');

// Admin olarak giriş yapmış mı kontrol et
if (!current_user_can('manage_options')) {
    die('Bu testi çalıştırmak için admin olarak giriş yapmalısınız.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Role Custom Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; border: 1px solid #c3e6cb; color: #155724; }
        .error { background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; }
        .warning { background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; }
        .info { background: #d1ecf1; border: 1px solid #bee5eb; color: #0c5460; }
        table { border-collapse: collapse; width: 100%; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Role Custom Eklenti Test Sonuçları</h1>
    
    <?php
    // Test 1: Eklenti aktif mi?
    if (class_exists('Role_Custom')) {
        echo '<div class="test-result success">✓ Role Custom eklentisi başarıyla yüklendi</div>';
    } else {
        echo '<div class="test-result error">✗ Role Custom eklentisi yüklenmedi</div>';
        exit;
    }
    
    // Test 2: Tutor LMS aktif mi?
    if (class_exists('TUTOR\Tutor')) {
        echo '<div class="test-result success">✓ Tutor LMS eklentisi aktif</div>';
    } else {
        echo '<div class="test-result warning">⚠ Tutor LMS eklentisi aktif değil</div>';
    }
    
    // Test 3: WooCommerce aktif mi?
    if (class_exists('WooCommerce')) {
        echo '<div class="test-result success">✓ WooCommerce eklentisi aktif</div>';
    } else {
        echo '<div class="test-result warning">⚠ WooCommerce eklentisi aktif değil</div>';
    }
    
    // Test 4: Tutor Instructor rolü var mı?
    if (get_role('tutor_instructor')) {
        echo '<div class="test-result success">✓ Tutor Instructor rolü mevcut</div>';
    } else {
        echo '<div class="test-result error">✗ Tutor Instructor rolü bulunamadı</div>';
    }
    
    // Test 5: Tutor Instructor kullanıcıları
    $instructors = get_users(['role' => 'tutor_instructor']);
    $instructor_count = count($instructors);
    
    if ($instructor_count > 0) {
        echo '<div class="test-result success">✓ ' . $instructor_count . ' adet Tutor Instructor kullanıcısı bulundu</div>';
    } else {
        echo '<div class="test-result info">ℹ Henüz Tutor Instructor kullanıcısı yok</div>';
    }
    
    // Test 6: Admin menü hook'ları
    global $wp_filter;
    $admin_menu_hooks = isset($wp_filter['admin_menu']) ? count($wp_filter['admin_menu']->callbacks) : 0;
    
    if ($admin_menu_hooks > 0) {
        echo '<div class="test-result success">✓ Admin menü hook\'ları aktif (' . $admin_menu_hooks . ' callback)</div>';
    } else {
        echo '<div class="test-result error">✗ Admin menü hook\'ları bulunamadı</div>';
    }
    
    // Test 7: CSS ve JS dosyaları
    $css_file = plugin_dir_path(__FILE__) . 'assets/css/admin.css';
    if (file_exists($css_file)) {
        echo '<div class="test-result success">✓ Admin CSS dosyası mevcut</div>';
    } else {
        echo '<div class="test-result error">✗ Admin CSS dosyası bulunamadı</div>';
    }
    
    // Test 8: Dil dosyası
    $lang_file = plugin_dir_path(__FILE__) . 'languages/role-custom.pot';
    if (file_exists($lang_file)) {
        echo '<div class="test-result success">✓ Dil dosyası mevcut</div>';
    } else {
        echo '<div class="test-result error">✗ Dil dosyası bulunamadı</div>';
    }
    ?>
    
    <h2>Tutor Instructor Kullanıcıları</h2>
    <?php if ($instructor_count > 0): ?>
        <table>
            <thead>
                <tr>
                    <th>ID</th>
                    <th>Kullanıcı Adı</th>
                    <th>Ad Soyad</th>
                    <th>E-posta</th>
                    <th>Kayıt Tarihi</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($instructors as $instructor): ?>
                    <tr>
                        <td><?php echo $instructor->ID; ?></td>
                        <td><?php echo esc_html($instructor->user_login); ?></td>
                        <td><?php echo esc_html($instructor->display_name); ?></td>
                        <td><?php echo esc_html($instructor->user_email); ?></td>
                        <td><?php echo date('d.m.Y H:i', strtotime($instructor->user_registered)); ?></td>
                    </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    <?php else: ?>
        <div class="test-result info">Henüz Tutor Instructor rolünde kullanıcı bulunmuyor.</div>
    <?php endif; ?>
    
    <h2>WordPress Menü Yapısı (Admin için)</h2>
    <div class="test-result info">
        <p>Role Custom menüsünü görmek için WordPress admin paneline gidin:</p>
        <ul>
            <li><strong>Admin Panel:</strong> <a href="<?php echo admin_url(); ?>" target="_blank"><?php echo admin_url(); ?></a></li>
            <li><strong>Role Custom Eğitmenler:</strong> <a href="<?php echo admin_url('admin.php?page=role-custom'); ?>" target="_blank">Eğitmenler Sayfası</a></li>
            <li><strong>Role Custom Ayarlar:</strong> <a href="<?php echo admin_url('admin.php?page=role-custom-settings'); ?>" target="_blank">Ayarlar Sayfası</a></li>
        </ul>
    </div>
    
    <h2>Test Tamamlandı</h2>
    <div class="test-result success">
        <p><strong>Sonuç:</strong> Role Custom eklentisi başarıyla kuruldu ve çalışıyor.</p>
        <p><strong>Önemli:</strong> Bu test dosyasını güvenlik nedeniyle silin!</p>
    </div>
    
    <script>
        // 10 saniye sonra uyarı göster
        setTimeout(function() {
            alert('Güvenlik uyarısı: Bu test dosyasını silin!');
        }, 10000);
    </script>
</body>
</html>
