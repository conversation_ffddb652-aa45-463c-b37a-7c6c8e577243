<?php
/**
 * Superole Kullanıcıları için Tutor LMS Eğitmen Rolü Düzeltme Scripti
 * 
 * Bu dosyayı WordPress root dizininde çalıştırarak mevcut superole kullanıcılarına
 * tutor_instructor rolünü ve gerekli meta verileri ekleyebilirsiniz.
 * 
 * Kullanım: WordPress root dizininde bu dosyayı çalıştırın
 * Örnek: php fix-superole-instructor.php
 */

// WordPress'i yükle
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die("WordPress bulunamadı! Bu dosyayı WordPress root dizininde çalıştırın.\n");
}

echo "=== Superole Kullanıcıları için Tutor LMS Eğitmen Rolü Düzeltme ===\n\n";

// 1. Tutor LMS aktif mi kontrol et
if (!class_exists('TUTOR\Tutor') || !function_exists('tutor_time')) {
    die("✗ Tutor LMS bulunamadı! Önce Tutor LMS eklentisini etkinleştirin.\n");
}

echo "✓ Tutor LMS aktif.\n";

// 2. Superole rolü var mı kontrol et
$superole_role = get_role('superole');
if (!$superole_role) {
    die("✗ Superole rolü bulunamadı! Önce Role Custom eklentisini etkinleştirin.\n");
}

echo "✓ Superole rolü mevcut.\n";

// 3. Tutor instructor rolünü al
if (function_exists('tutor') && isset(tutor()->instructor_role)) {
    $instructor_role = tutor()->instructor_role;
} else {
    $instructor_role = 'tutor_instructor';
}

echo "✓ Tutor instructor rolü: {$instructor_role}\n\n";

// 4. Tüm superole kullanıcılarını al
$superole_users = get_users(['role' => 'superole']);

if (empty($superole_users)) {
    echo "ℹ️  Henüz hiçbir kullanıcı superole rolüne sahip değil.\n";
    exit(0);
}

echo "📊 Toplam " . count($superole_users) . " superole kullanıcısı bulundu.\n\n";

// 5. Her kullanıcı için düzeltme yap
$fixed_count = 0;
$already_ok_count = 0;

foreach ($superole_users as $user) {
    echo "👤 Kullanıcı: {$user->display_name} ({$user->user_login}) - ID: {$user->ID}\n";
    
    $needs_fix = false;
    
    // Meta verileri kontrol et
    $is_instructor = get_user_meta($user->ID, '_is_tutor_instructor', true);
    $instructor_status = get_user_meta($user->ID, '_tutor_instructor_status', true);
    $instructor_approved = get_user_meta($user->ID, '_tutor_instructor_approved', true);
    
    echo "   - _is_tutor_instructor: " . ($is_instructor ? "✓" : "✗") . "\n";
    echo "   - _tutor_instructor_status: " . ($instructor_status ? "✓ ({$instructor_status})" : "✗") . "\n";
    echo "   - _tutor_instructor_approved: " . ($instructor_approved ? "✓" : "✗") . "\n";
    
    // Tutor instructor rolü kontrol et
    $has_tutor_role = in_array($instructor_role, $user->roles);
    echo "   - {$instructor_role} rolü: " . ($has_tutor_role ? "✓" : "✗") . "\n";
    
    // Meta veriler eksikse ekle
    if (!$is_instructor) {
        update_user_meta($user->ID, '_is_tutor_instructor', tutor_time());
        echo "   ➕ _is_tutor_instructor meta verisi eklendi\n";
        $needs_fix = true;
    }
    
    if (!$instructor_status || $instructor_status !== 'approved') {
        update_user_meta($user->ID, '_tutor_instructor_status', 'approved');
        echo "   ➕ _tutor_instructor_status 'approved' olarak ayarlandı\n";
        $needs_fix = true;
    }
    
    if (!$instructor_approved) {
        update_user_meta($user->ID, '_tutor_instructor_approved', tutor_time());
        echo "   ➕ _tutor_instructor_approved meta verisi eklendi\n";
        $needs_fix = true;
    }
    
    // Tutor instructor rolü eksikse ekle
    if (!$has_tutor_role) {
        $user->add_role($instructor_role);
        echo "   ➕ {$instructor_role} rolü eklendi\n";
        $needs_fix = true;
    }
    
    if ($needs_fix) {
        // Tutor LMS hook'larını tetikle
        do_action('tutor_before_approved_instructor', $user->ID);
        do_action('tutor_after_approved_instructor', $user->ID);
        
        echo "   ✅ Kullanıcı düzeltildi!\n";
        $fixed_count++;
    } else {
        echo "   ✅ Kullanıcı zaten tamam!\n";
        $already_ok_count++;
    }
    
    echo "\n";
}

// 6. Özet
echo "=== ÖZET ===\n";
echo "📊 Toplam kullanıcı: " . count($superole_users) . "\n";
echo "✅ Düzeltilen kullanıcı: {$fixed_count}\n";
echo "✓ Zaten tamam olan kullanıcı: {$already_ok_count}\n";

if ($fixed_count > 0) {
    echo "\n🎉 Düzeltme işlemi tamamlandı!\n";
    echo "💡 Artık superole kullanıcıları kurs oluşturabilir.\n";
    
    // Setup flag'ini güncelle
    update_option('role_custom_superole_instructor_setup_done', true);
    echo "✓ Kurulum flag'i güncellendi.\n";
} else {
    echo "\n✨ Tüm kullanıcılar zaten doğru şekilde yapılandırılmış!\n";
}

echo "\n=== İşlem Tamamlandı ===\n";
?>
