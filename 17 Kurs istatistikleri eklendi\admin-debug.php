<?php
/**
 * Role Custom Debug Admin Sayfası
 * 
 * Bu sayfa 36 eklentisindeki gibi meta veri temizleme işlemlerini
 * admin panelinden yapabilmek için oluşturulmuştur.
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

// Admin sayfası oluştur
add_action('admin_menu', 'role_custom_add_debug_menu');

function role_custom_add_debug_menu() {
    add_submenu_page(
        'users.php',
        'Role Custom Debug',
        'Role Custom Debug',
        'manage_options',
        'role-custom-debug',
        'role_custom_debug_page'
    );
}

function role_custom_debug_page() {
    // AJAX işlemlerini handle et
    if (isset($_POST['action']) && $_POST['action'] === 'clean_instructor_meta') {
        check_admin_referer('role_custom_debug_nonce');
        
        $user_id = intval($_POST['user_id']);
        if ($user_id && get_userdata($user_id)) {
            $result = role_custom_clean_instructor_meta_data($user_id);
            if ($result) {
                echo '<div class="notice notice-success"><p>Kullanıcı ID ' . $user_id . ' için eğitmen meta verileri başarıyla temizlendi.</p></div>';
            } else {
                echo '<div class="notice notice-error"><p>Meta veri temizleme işlemi başarısız oldu.</p></div>';
            }
        } else {
            echo '<div class="notice notice-error"><p>Geçersiz kullanıcı ID\'si.</p></div>';
        }
    }
    
    if (isset($_POST['action']) && $_POST['action'] === 'test_role_change') {
        check_admin_referer('role_custom_debug_nonce');
        
        $user_id = intval($_POST['user_id']);
        $new_role = sanitize_text_field($_POST['new_role']);
        
        if ($user_id && get_userdata($user_id) && $new_role) {
            $user = new WP_User($user_id);
            $old_roles = $user->roles;
            $user->set_role($new_role);
            
            echo '<div class="notice notice-success"><p>Kullanıcı ID ' . $user_id . ' rolü "' . $new_role . '" olarak değiştirildi. Eski roller: ' . implode(', ', $old_roles) . '</p></div>';
        } else {
            echo '<div class="notice notice-error"><p>Geçersiz kullanıcı ID\'si veya rol.</p></div>';
        }
    }
    
    ?>
    <div class="wrap">
        <h1>Role Custom Debug</h1>
        <p>Bu sayfa eğitmen meta verilerini temizlemek ve rol değişikliklerini test etmek için kullanılır.</p>
        
        <div style="display: flex; gap: 20px;">
            <!-- Meta Veri Temizleme -->
            <div style="flex: 1; background: #fff; padding: 20px; border: 1px solid #ccd0d4;">
                <h2>Eğitmen Meta Verilerini Temizle</h2>
                <p>36 eklentisindeki gibi kapsamlı meta veri temizleme işlemi yapar.</p>
                
                <form method="post">
                    <?php wp_nonce_field('role_custom_debug_nonce'); ?>
                    <input type="hidden" name="action" value="clean_instructor_meta">
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">Kullanıcı ID</th>
                            <td>
                                <input type="number" name="user_id" required min="1" style="width: 100px;">
                                <p class="description">Temizlenecek kullanıcının ID'sini girin</p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Meta Verileri Temizle" 
                               onclick="return confirm('Bu işlem kullanıcının tüm eğitmen meta verilerini silecek. Devam etmek istiyor musunuz?')">
                    </p>
                </form>
            </div>
            
            <!-- Rol Değişikliği Testi -->
            <div style="flex: 1; background: #fff; padding: 20px; border: 1px solid #ccd0d4;">
                <h2>Rol Değişikliği Testi</h2>
                <p>Kullanıcı rolünü değiştirerek hook'ların çalışıp çalışmadığını test edin.</p>
                
                <form method="post">
                    <?php wp_nonce_field('role_custom_debug_nonce'); ?>
                    <input type="hidden" name="action" value="test_role_change">
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">Kullanıcı ID</th>
                            <td>
                                <input type="number" name="user_id" required min="1" style="width: 100px;">
                                <p class="description">Test edilecek kullanıcının ID'sini girin</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Yeni Rol</th>
                            <td>
                                <select name="new_role" required>
                                    <option value="">Rol Seçin</option>
                                    <option value="subscriber">Abone (Subscriber)</option>
                                    <option value="tutor_instructor">Tutor Eğitmen</option>
                                    <option value="superole">Superole</option>
                                    <option value="administrator">Yönetici</option>
                                </select>
                                <p class="description">Kullanıcının yeni rolünü seçin</p>
                            </td>
                        </tr>
                    </table>
                    
                    <p class="submit">
                        <input type="submit" class="button-primary" value="Rolü Değiştir">
                    </p>
                </form>
            </div>
        </div>
        
        <!-- Kullanıcı Listesi -->
        <div style="margin-top: 20px; background: #fff; padding: 20px; border: 1px solid #ccd0d4;">
            <h2>Kullanıcı Listesi</h2>
            <p>Sistemdeki kullanıcıları ve rollerini görüntüleyin.</p>
            
            <?php
            $users = get_users(['number' => 20]);
            if ($users) {
                echo '<table class="wp-list-table widefat fixed striped">';
                echo '<thead><tr><th>ID</th><th>Kullanıcı Adı</th><th>E-posta</th><th>Roller</th><th>Eğitmen Meta</th></tr></thead>';
                echo '<tbody>';
                
                foreach ($users as $user) {
                    $is_instructor_meta = get_user_meta($user->ID, '_is_tutor_instructor', true);
                    $instructor_status = get_user_meta($user->ID, '_tutor_instructor_status', true);
                    
                    echo '<tr>';
                    echo '<td>' . $user->ID . '</td>';
                    echo '<td>' . $user->user_login . '</td>';
                    echo '<td>' . $user->user_email . '</td>';
                    echo '<td>' . implode(', ', $user->roles) . '</td>';
                    echo '<td>';
                    if ($is_instructor_meta) {
                        echo '✓ Eğitmen (' . $instructor_status . ')';
                    } else {
                        echo '✗ Değil';
                    }
                    echo '</td>';
                    echo '</tr>';
                }
                
                echo '</tbody></table>';
            } else {
                echo '<p>Kullanıcı bulunamadı.</p>';
            }
            ?>
        </div>
        
        <!-- Test Sonuçları -->
        <div style="margin-top: 20px; background: #fff; padding: 20px; border: 1px solid #ccd0d4;">
            <h2>Test Komutları</h2>
            <p>Terminal'den çalıştırabileceğiniz test komutları:</p>
            
            <code style="display: block; background: #f1f1f1; padding: 10px; margin: 10px 0;">
                # Dinamik eğitmen sistemini test et<br>
                php role-custom/test-dynamic-instructor.php
            </code>
            
            <code style="display: block; background: #f1f1f1; padding: 10px; margin: 10px 0;">
                # Rol değişikliği testini çalıştır<br>
                php role-custom/test-role-change.php
            </code>
            
            <code style="display: block; background: #f1f1f1; padding: 10px; margin: 10px 0;">
                # Mevcut kullanıcıyı test et (USER_ID yerine gerçek ID yazın)<br>
                php role-custom/test-role-change.php USER_ID
            </code>
        </div>
    </div>
    <?php
}
?>
