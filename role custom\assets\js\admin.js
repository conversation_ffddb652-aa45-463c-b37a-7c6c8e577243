/**
 * Role Custom Admin JavaScript
 * Eğitmenler sayfası için rol yönetimi popup'ı
 */

jQuery(document).ready(function($) {
    'use strict';

    // Modal elementleri
    const $modal = $('#role-custom-modal');
    const $modalContent = $('.role-custom-modal-content');
    const $closeBtn = $('.role-custom-modal-close');
    const $cancelBtn = $('.role-custom-modal-cancel');
    const $saveRoleBtn = $('#role-custom-save-role');
    const $deleteMetaBtn = $('#role-custom-delete-meta');
    const $userNameSpan = $('#role-custom-user-name');
    const $currentRoleSpan = $('#role-custom-current-role');
    const $newRoleSelect = $('#role-custom-new-role');

    let currentUserId = null;

    // Rol yönet butonlarına click event'i ekle
    $(document).on('click', '.role-custom-role-btn', function(e) {
        e.preventDefault();
        
        const $btn = $(this);
        currentUserId = $btn.data('user-id');
        const userName = $btn.data('user-name');
        const currentRole = $btn.data('current-role');

        // Modal bilgilerini güncelle
        $userNameSpan.text(userName);
        $currentRoleSpan.text(currentRole);
        
        // Mevcut rolü select'te seç
        const firstRole = currentRole.split(',')[0].trim();
        $newRoleSelect.val(firstRole);

        // Modal'ı göster
        showModal();
    });

    // Modal'ı göster
    function showModal() {
        $modal.fadeIn(300);
        $('body').addClass('role-custom-modal-open');
    }

    // Modal'ı gizle
    function hideModal() {
        $modal.fadeOut(300);
        $('body').removeClass('role-custom-modal-open');
        currentUserId = null;
    }

    // Modal kapatma event'leri
    $closeBtn.on('click', hideModal);
    $cancelBtn.on('click', hideModal);

    // Modal dışına tıklayınca kapat
    $modal.on('click', function(e) {
        if (e.target === this) {
            hideModal();
        }
    });

    // ESC tuşu ile kapat
    $(document).on('keydown', function(e) {
        if (e.keyCode === 27 && $modal.is(':visible')) {
            hideModal();
        }
    });

    // Rolü kaydet butonu
    $saveRoleBtn.on('click', function(e) {
        e.preventDefault();

        if (!currentUserId) {
            alert(roleCustomAjax.messages.error);
            return;
        }

        const newRole = $newRoleSelect.val();
        if (!newRole) {
            alert('Lütfen bir rol seçin.');
            return;
        }

        // Onay iste
        if (!confirm(roleCustomAjax.messages.confirmRoleChange)) {
            return;
        }

        // Buton durumunu güncelle
        const $btn = $(this);
        const originalText = $btn.text();
        $btn.prop('disabled', true).text(roleCustomAjax.messages.loading);

        // AJAX isteği
        $.ajax({
            url: roleCustomAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_update_user_role',
                nonce: roleCustomAjax.nonce,
                user_id: currentUserId,
                new_role: newRole
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    hideModal();
                    // Sayfayı yenile
                    location.reload();
                } else {
                    alert(response.data || roleCustomAjax.messages.error);
                }
            },
            error: function() {
                alert(roleCustomAjax.messages.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Meta verileri sil butonu
    $deleteMetaBtn.on('click', function(e) {
        e.preventDefault();

        if (!currentUserId) {
            alert(roleCustomAjax.messages.error);
            return;
        }

        // Onay iste
        if (!confirm(roleCustomAjax.messages.confirmDeleteMeta)) {
            return;
        }

        // Buton durumunu güncelle
        const $btn = $(this);
        const originalText = $btn.text();
        $btn.prop('disabled', true).text(roleCustomAjax.messages.loading);

        // AJAX isteği
        $.ajax({
            url: roleCustomAjax.ajaxUrl,
            type: 'POST',
            data: {
                action: 'role_custom_delete_user_meta',
                nonce: roleCustomAjax.nonce,
                user_id: currentUserId
            },
            success: function(response) {
                if (response.success) {
                    alert(response.data.message);
                    // Modal'ı kapatma, kullanıcı isterse rol değişikliği de yapabilir
                } else {
                    alert(response.data || roleCustomAjax.messages.error);
                }
            },
            error: function() {
                alert(roleCustomAjax.messages.error);
            },
            complete: function() {
                $btn.prop('disabled', false).text(originalText);
            }
        });
    });

    // Modal animasyonları için CSS sınıfları
    $modal.on('click', '.role-custom-modal-content', function(e) {
        e.stopPropagation();
    });

    // Form submit'ini engelle
    $modal.on('submit', 'form', function(e) {
        e.preventDefault();
    });

    // Select değişikliklerini takip et
    $newRoleSelect.on('change', function() {
        const selectedRole = $(this).val();
        const selectedText = $(this).find('option:selected').text();
        
        // Seçilen rolü vurgula
        if (selectedRole) {
            $(this).removeClass('role-custom-select-empty');
        } else {
            $(this).addClass('role-custom-select-empty');
        }
    });

    // Sayfa yüklendiğinde modal'ın gizli olduğundan emin ol
    $modal.hide();
    
    // Debug için console log
    console.log('Role Custom Admin JS loaded successfully');
});
