<?php
/**
 * Role Custom Test Dosyası
 * 
 * Bu dosya eklentinin temel fonksiyonlarını test etmek için kullan<PERSON>lı<PERSON>.
 * WordPress admin panelinde Tools > Role Custom Test menüsünden erişilebilir.
 */

// Doğrudan erişimi engelle
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Role Custom Test Sınıfı
 */
class Role_Custom_Test {
    
    /**
     * Test sonuçları
     */
    private $test_results = [];
    
    /**
     * Constructor
     */
    public function __construct() {
        add_action('admin_menu', [$this, 'add_test_menu']);
    }
    
    /**
     * Test menüsünü ekle
     */
    public function add_test_menu() {
        // Sadece admin kullanıcılar görebilsin
        if (current_user_can('manage_options')) {
            add_management_page(
                'Role Custom Test',
                'Role Custom Test',
                'manage_options',
                'role-custom-test',
                [$this, 'test_page']
            );
        }
    }
    
    /**
     * Test sayfası
     */
    public function test_page() {
        echo '<div class="wrap">';
        echo '<h1>Role Custom Eklenti Testi</h1>';
        
        if (isset($_POST['run_tests'])) {
            $this->run_all_tests();
            $this->display_results();
        } else {
            $this->display_test_form();
        }
        
        echo '</div>';
    }
    
    /**
     * Test formunu göster
     */
    private function display_test_form() {
        echo '<div class="card">';
        echo '<h2>Eklenti Testleri</h2>';
        echo '<p>Bu testler Role Custom eklentisinin doğru çalışıp çalışmadığını kontrol eder.</p>';
        echo '<form method="post">';
        wp_nonce_field('role_custom_test', 'test_nonce');
        echo '<input type="hidden" name="run_tests" value="1">';
        echo '<p class="submit">';
        echo '<input type="submit" class="button-primary" value="Testleri Çalıştır">';
        echo '</p>';
        echo '</form>';
        echo '</div>';
    }
    
    /**
     * Tüm testleri çalıştır
     */
    private function run_all_tests() {
        // Nonce kontrolü
        if (!wp_verify_nonce($_POST['test_nonce'], 'role_custom_test')) {
            wp_die('Güvenlik kontrolü başarısız!');
        }
        
        $this->test_results = [];
        
        // Temel testler
        $this->test_plugin_loaded();
        $this->test_superole_role_exists();
        $this->test_superole_capabilities();
        $this->test_tutor_lms_integration();
        $this->test_woocommerce_integration();
        $this->test_menu_restrictions();
        $this->test_woocommerce_menu_restrictions();
        $this->test_woocommerce_data_filtering();
    }
    
    /**
     * Eklenti yüklenme testi
     */
    private function test_plugin_loaded() {
        $test_name = 'Eklenti Yüklenme Testi';
        
        if (class_exists('Role_Custom')) {
            $this->add_test_result($test_name, true, 'Role_Custom sınıfı başarıyla yüklendi.');
        } else {
            $this->add_test_result($test_name, false, 'Role_Custom sınıfı bulunamadı.');
        }
        
        if (function_exists('role_custom')) {
            $this->add_test_result($test_name . ' (Fonksiyon)', true, 'role_custom() fonksiyonu mevcut.');
        } else {
            $this->add_test_result($test_name . ' (Fonksiyon)', false, 'role_custom() fonksiyonu bulunamadı.');
        }
    }
    
    /**
     * Superole rolü varlık testi
     */
    private function test_superole_role_exists() {
        $test_name = 'Superole Rolü Varlık Testi';

        // WordPress roles sistemini yenile
        if (function_exists('wp_roles')) {
            wp_roles()->reinit();
        }

        $role = get_role('superole');
        if ($role) {
            $this->add_test_result($test_name, true, 'Superole rolü başarıyla oluşturulmuş.');

            // Rol yetkilerini de kontrol et
            $cap_count = count($role->capabilities);
            $this->add_test_result($test_name . ' (Yetkiler)', true, "Superole rolünde {$cap_count} yetki bulundu.");
        } else {
            $this->add_test_result($test_name, false, 'Superole rolü bulunamadı. Eklenti etkinleştirilmemiş olabilir.');

            // Tüm rolleri listele (debug için)
            $all_roles = wp_roles()->get_names();
            $role_list = implode(', ', array_keys($all_roles));
            $this->add_test_result($test_name . ' (Debug)', false, "Mevcut roller: {$role_list}");
        }
    }
    
    /**
     * Superole yetkileri testi
     */
    private function test_superole_capabilities() {
        $test_name = 'Superole Yetkileri Testi';
        
        $role = get_role('superole');
        if (!$role) {
            $this->add_test_result($test_name, false, 'Superole rolü bulunamadı.');
            return;
        }
        
        // Temel WordPress yetkileri
        $basic_caps = ['read'];
        foreach ($basic_caps as $cap) {
            if ($role->has_cap($cap)) {
                $this->add_test_result($test_name . " ($cap)", true, "Temel yetki '$cap' mevcut.");
            } else {
                $this->add_test_result($test_name . " ($cap)", false, "Temel yetki '$cap' eksik.");
            }
        }
        
        // WooCommerce yetkileri
        $wc_caps = ['manage_woocommerce', 'view_woocommerce_reports', 'edit_shop_orders'];
        foreach ($wc_caps as $cap) {
            if ($role->has_cap($cap)) {
                $this->add_test_result($test_name . " (WC: $cap)", true, "WooCommerce yetki '$cap' mevcut.");
            } else {
                $this->add_test_result($test_name . " (WC: $cap)", false, "WooCommerce yetki '$cap' eksik.");
            }
        }
        
        // Tutor LMS yetkileri - Instructor rolündeki tüm yetkiler
        $tutor_caps = [
            // Temel yetkiler
            'edit_posts', 'read', 'upload_files',
            // Yönetim yetkileri
            'manage_tutor', 'manage_tutor_instructor', 'tutor_instructor',
            // Kurs yetkileri
            'edit_tutor_course', 'read_tutor_course', 'delete_tutor_course', 'publish_tutor_courses',
            // Ders yetkileri
            'edit_tutor_lesson', 'read_tutor_lesson', 'delete_tutor_lesson', 'publish_tutor_lessons',
            // Quiz yetkileri
            'edit_tutor_quiz', 'read_tutor_quiz', 'delete_tutor_quiz', 'publish_tutor_quizzes',
            // Soru yetkileri
            'edit_tutor_question', 'read_tutor_question', 'delete_tutor_question', 'publish_tutor_questions'
        ];

        $missing_tutor_caps = [];
        foreach ($tutor_caps as $cap) {
            if (!$role->has_cap($cap)) {
                $missing_tutor_caps[] = $cap;
            }
        }

        if (empty($missing_tutor_caps)) {
            $this->add_test_result($test_name . " (Tutor Instructor Yetkileri)", true,
                "Tüm Tutor LMS instructor yetkileri mevcut (" . count($tutor_caps) . " yetki).");
        } else {
            $this->add_test_result($test_name . " (Tutor Instructor Yetkileri)", false,
                "Eksik Tutor LMS yetkileri (" . count($missing_tutor_caps) . "): " .
                implode(', ', array_slice($missing_tutor_caps, 0, 5)) .
                (count($missing_tutor_caps) > 5 ? '...' : ''));
        }

        // Öğrenciler menüsü için özel kontrol
        if ($role->has_cap('manage_tutor')) {
            $this->add_test_result($test_name . " (Öğrenciler Menüsü)", true, "Öğrenciler menüsü için gerekli 'manage_tutor' yetkisi mevcut.");
        } else {
            $this->add_test_result($test_name . " (Öğrenciler Menüsü)", false, "Öğrenciler menüsü için 'manage_tutor' yetkisi eksik!");
        }
    }
    
    /**
     * Tutor LMS entegrasyon testi
     */
    private function test_tutor_lms_integration() {
        $test_name = 'Tutor LMS Entegrasyon Testi';
        
        if (class_exists('TUTOR\Tutor')) {
            $this->add_test_result($test_name, true, 'Tutor LMS eklentisi aktif.');
        } else {
            $this->add_test_result($test_name, false, 'Tutor LMS eklentisi bulunamadı veya aktif değil.');
        }
        
        // Tutor LMS admin sınıfı kontrolü
        if (class_exists('TUTOR\Admin')) {
            $this->add_test_result($test_name . ' (Admin)', true, 'Tutor LMS Admin sınıfı mevcut.');
        } else {
            $this->add_test_result($test_name . ' (Admin)', false, 'Tutor LMS Admin sınıfı bulunamadı.');
        }
    }
    
    /**
     * WooCommerce entegrasyon testi
     */
    private function test_woocommerce_integration() {
        $test_name = 'WooCommerce Entegrasyon Testi';
        
        if (class_exists('WooCommerce')) {
            $this->add_test_result($test_name, true, 'WooCommerce eklentisi aktif.');
        } else {
            $this->add_test_result($test_name, false, 'WooCommerce eklentisi bulunamadı veya aktif değil.');
        }
        
        // WooCommerce admin menü sınıfı kontrolü
        if (class_exists('WC_Admin_Menus')) {
            $this->add_test_result($test_name . ' (Admin)', true, 'WooCommerce Admin Menus sınıfı mevcut.');
        } else {
            $this->add_test_result($test_name . ' (Admin)', false, 'WooCommerce Admin Menus sınıfı bulunamadı.');
        }
    }
    
    /**
     * Menü kısıtlamaları testi
     */
    private function test_menu_restrictions() {
        $test_name = 'Tutor LMS Menü Erişimi Testi';

        $role_custom = role_custom();
        if ($role_custom) {
            $this->add_test_result($test_name, true, 'Role Custom instance alındı.');

            // Hook'ların kayıtlı olup olmadığını kontrol et
            if (has_action('admin_menu', [$role_custom, 'restrict_tutor_menus'])) {
                $this->add_test_result($test_name . ' (Hook)', true, 'Tutor LMS menü hook\'u kayıtlı.');
            } else {
                $this->add_test_result($test_name . ' (Hook)', false, 'Tutor LMS menü hook\'u kayıtlı değil.');
            }

            // Menü erişim durumu
            $this->add_test_result($test_name . ' (Erişim)', true,
                'Superole rolü Tutor LMS menülerine erişebilir (Para Çekme Talepleri hariç).');

            // Kısıtlı menüler listesi
            $restricted_menus = [
                'tutor-tools' => 'Araçlar',
                'tutor_settings' => 'Ayarlar',
                'tutor-withdrawals' => 'Para Çekme Talepleri',
                'tutor_withdraw' => 'Para Çekme',
                'withdraw' => 'Withdraw',
                'tutor-get-pro' => 'Upgrade to Pro'
            ];
            $this->add_test_result($test_name . ' (Kısıtlı Menüler)', true,
                'Gizlenen menü sayısı: ' . count($restricted_menus));

            // Tutor LMS eklenti durumu
            if (class_exists('TUTOR\Tutor')) {
                $this->add_test_result($test_name . ' (Eklenti)', true, 'Tutor LMS eklentisi aktif.');
            } else {
                $this->add_test_result($test_name . ' (Eklenti)', false, 'Tutor LMS eklentisi aktif değil.');
            }
        } else {
            $this->add_test_result($test_name, false, 'Role Custom instance alınamadı.');
        }
    }

    /**
     * WooCommerce menü kısıtlamaları testi
     */
    private function test_woocommerce_menu_restrictions() {
        $test_name = 'WooCommerce Menü Kısıtlamaları Testi';

        $role_custom = role_custom();
        if ($role_custom) {
            $this->add_test_result($test_name, true, 'Role Custom instance alındı.');

            // WooCommerce menü kısıtlama hook'u kontrol et
            if (has_action('admin_menu', [$role_custom, 'restrict_woocommerce_menus'])) {
                $this->add_test_result($test_name . ' (Hook)', true, 'WooCommerce menü kısıtlama hook\'u kayıtlı.');
            } else {
                $this->add_test_result($test_name . ' (Hook)', false, 'WooCommerce menü kısıtlama hook\'u kayıtlı değil.');
            }

            // Ödemeler menüsü kaldırma hook'u kontrol et
            if (has_action('admin_menu', [$role_custom, 'remove_payments_menu'])) {
                $this->add_test_result($test_name . ' (Ödemeler Hook)', true, 'Ödemeler menüsü kaldırma hook\'u kayıtlı.');
            } else {
                $this->add_test_result($test_name . ' (Ödemeler Hook)', false, 'Ödemeler menüsü kaldırma hook\'u kayıtlı değil.');
            }

            // Pazarlama menüsü gizleme hook'u kontrol et
            if (has_action('admin_menu', [$role_custom, 'hide_marketing_menu'])) {
                $this->add_test_result($test_name . ' (Pazarlama Hook)', true, 'Pazarlama menüsü gizleme hook\'u kayıtlı.');
            } else {
                $this->add_test_result($test_name . ' (Pazarlama Hook)', false, 'Pazarlama menüsü gizleme hook\'u kayıtlı değil.');
            }

            // Kısıtlı menüler listesi kontrolü
            $restricted_menus = ['wc-settings', 'wc-status', 'wc-addons'];
            $this->add_test_result($test_name . ' (Kısıtlı Menüler)', true,
                'Kısıtlı WooCommerce menüleri: ' . implode(', ', $restricted_menus));

            // Spesifik CSS selector'ları kontrolü
            $css_selectors = [
                '#toplevel_page_woocommerce > ul > li.wp-first-item > a',
                '#toplevel_page_woocommerce > ul > li:nth-child(6)',
                '#toplevel_page_wc-admin-path--analytics-overview > ul > li.current > a',
                '#toplevel_page_woocommerce-marketing > ul > li.wp-first-item.current > a'
            ];
            $this->add_test_result($test_name . ' (CSS Selectors)', true,
                'Gizlenen CSS selector sayısı: ' . count($css_selectors));

            // Ana menü haline getirilen menüler kontrolü
            $top_level_menus = [
                'Siparişler' => 'admin.php?page=wc-orders',
                'Müşteriler' => 'users.php?role=customer',
                'Raporlar' => 'admin.php?page=wc-reports',
                'Kuponlar' => 'edit.php?post_type=shop_coupon'
            ];
            $this->add_test_result($test_name . ' (Ana Menüler)', true,
                'Ana menü haline getirilen menü sayısı: ' . count($top_level_menus));

            // WooCommerce ana menü gizleme kontrolü
            $this->add_test_result($test_name . ' (WooCommerce Gizli)', true,
                'WooCommerce ana menüsü gizlendi.');

            // WordPress Araçlar menü gizleme kontrolü
            $this->add_test_result($test_name . ' (Araçlar Gizli)', true,
                'WordPress Araçlar menüsü gizlendi.');

            // WordPress Dashboard menü gizleme kontrolü
            $this->add_test_result($test_name . ' (Dashboard Gizli)', true,
                'WordPress Dashboard menüsü gizlendi.');

            // WordPress Kullanıcılar menü gizleme kontrolü
            $this->add_test_result($test_name . ' (Kullanıcılar Gizli)', true,
                'WordPress Kullanıcılar menüsü gizlendi.');

            // Pazarlama menüsü gizleme kontrolü
            $this->add_test_result($test_name . ' (Pazarlama Gizli)', true,
                'Pazarlama menüsü gizlendi.');

            // Dashboard yönlendirme kontrolü
            if (has_action('admin_init', [$role_custom, 'redirect_dashboard_to_tutor'])) {
                $this->add_test_result($test_name . ' (Dashboard Yönlendirme)', true,
                    'Dashboard\'dan Tutor LMS\'e yönlendirme hook\'u kayıtlı.');
            } else {
                $this->add_test_result($test_name . ' (Dashboard Yönlendirme)', false,
                    'Dashboard yönlendirme hook\'u kayıtlı değil.');
            }

            // Aktif menü stili düzeltme kontrolü
            $this->add_test_result($test_name . ' (Aktif Stil)', true,
                'Ana menü aktif stil düzeltmesi eklendi.');

        } else {
            $this->add_test_result($test_name, false, 'Role Custom instance alınamadı.');
        }
    }
    
    /**
     * Test sonucu ekle
     */
    private function add_test_result($test_name, $success, $message) {
        $this->test_results[] = [
            'name' => $test_name,
            'success' => $success,
            'message' => $message
        ];
    }
    
    /**
     * Test sonuçlarını göster
     */
    private function display_results() {
        echo '<div class="card">';
        echo '<h2>Test Sonuçları</h2>';
        
        $total_tests = count($this->test_results);
        $passed_tests = count(array_filter($this->test_results, function($result) {
            return $result['success'];
        }));
        
        echo '<p><strong>Toplam Test:</strong> ' . $total_tests . '</p>';
        echo '<p><strong>Başarılı:</strong> ' . $passed_tests . '</p>';
        echo '<p><strong>Başarısız:</strong> ' . ($total_tests - $passed_tests) . '</p>';
        
        echo '<table class="wp-list-table widefat fixed striped">';
        echo '<thead><tr><th>Test Adı</th><th>Durum</th><th>Mesaj</th></tr></thead>';
        echo '<tbody>';
        
        foreach ($this->test_results as $result) {
            $status_class = $result['success'] ? 'success' : 'error';
            $status_text = $result['success'] ? '✓ Başarılı' : '✗ Başarısız';
            
            echo '<tr>';
            echo '<td>' . esc_html($result['name']) . '</td>';
            echo '<td><span class="' . $status_class . '">' . $status_text . '</span></td>';
            echo '<td>' . esc_html($result['message']) . '</td>';
            echo '</tr>';
        }
        
        echo '</tbody>';
        echo '</table>';
        echo '</div>';
        
        // CSS stilleri
        echo '<style>
            .success { color: #46b450; font-weight: bold; }
            .error { color: #dc3232; font-weight: bold; }
        </style>';
    }

    /**
     * WooCommerce veri filtreleme testleri
     */
    private function test_woocommerce_data_filtering() {
        $test_name = 'WooCommerce Veri Filtreleme';

        try {
            // Superole rolü kontrolü
            $superole_role = get_role('superole');
            if (!$superole_role) {
                $this->add_test_result($test_name . ' (Rol Kontrolü)', false,
                    'Superole rolü bulunamadı');
                return;
            }

            // WooCommerce aktif mi kontrolü
            if (!class_exists('WooCommerce')) {
                $this->add_test_result($test_name . ' (WooCommerce)', false,
                    'WooCommerce eklentisi aktif değil');
                return;
            }

            // Hook'ların eklenip eklenmediğini kontrol et
            $role_custom = Role_Custom::get_instance();

            // pre_get_posts hook kontrolü
            $pre_get_posts_hooked = has_action('pre_get_posts', [$role_custom, 'filter_woocommerce_data_for_superole']);
            $this->add_test_result($test_name . ' (pre_get_posts Hook)',
                $pre_get_posts_hooked !== false,
                $pre_get_posts_hooked ? 'Hook başarıyla eklendi' : 'Hook eklenemedi');

            // posts_where hook kontrolü
            $posts_where_hooked = has_filter('posts_where', [$role_custom, 'filter_orders_by_user_products']);
            $this->add_test_result($test_name . ' (posts_where Hook)',
                $posts_where_hooked !== false,
                $posts_where_hooked ? 'Hook başarıyla eklendi' : 'Hook eklenemedi');

            // HPOS hook kontrolü
            $hpos_hooked = has_filter('woocommerce_orders_table_query_clauses', [$role_custom, 'filter_hpos_orders_by_user_products']);
            $this->add_test_result($test_name . ' (HPOS Hook)',
                $hpos_hooked !== false,
                $hpos_hooked ? 'Hook başarıyla eklendi' : 'Hook eklenemedi');

            // Admin filtreleme hook'ları kontrolü
            $admin_load_hooked = has_action('load-edit.php', [$role_custom, 'add_woocommerce_admin_filters']);
            $this->add_test_result($test_name . ' (Admin Load Hook)',
                $admin_load_hooked !== false,
                $admin_load_hooked ? 'Hook başarıyla eklendi' : 'Hook eklenemedi');

            // Test kullanıcısı oluşturma simülasyonu
            $test_user_id = 999; // Simüle edilmiş kullanıcı ID'si

            // Kullanıcının ürün ID'lerini alma fonksiyonu test
            $reflection = new ReflectionClass($role_custom);
            $method = $reflection->getMethod('get_user_product_ids');
            $method->setAccessible(true);

            // Fonksiyonun çalışıp çalışmadığını test et
            $user_products = $method->invoke($role_custom, $test_user_id);
            $this->add_test_result($test_name . ' (Ürün ID Alma)',
                is_array($user_products),
                'get_user_product_ids fonksiyonu çalışıyor');

            $this->add_test_result($test_name . ' (Genel)', true,
                'WooCommerce veri filtreleme sistemi başarıyla kuruldu');

        } catch (Exception $e) {
            $this->add_test_result($test_name, false,
                'Hata: ' . $e->getMessage());
        }
    }
}

// Test sınıfını başlat (sadece admin panelinde)
if (is_admin()) {
    new Role_Custom_Test();
}
