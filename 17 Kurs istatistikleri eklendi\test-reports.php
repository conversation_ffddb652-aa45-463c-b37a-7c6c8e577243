<?php
/**
 * Role Custom Reports Test File
 * Bu dosya Reports özelliğinin test edilmesi için kullanılır
 */

// WordPress yüklenmesini bekle
if (!defined('ABSPATH')) {
    exit('WordPress yüklenmedi!');
}

/**
 * Reports özelliğini test et
 */
function role_custom_test_reports() {
    echo "<h2>Role Custom Reports Test</h2>";
    
    // Eklenti aktif mi?
    if (!class_exists('Role_Custom')) {
        echo "<p style='color: red;'>❌ Role Custom eklentisi aktif değil!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Role Custom eklentisi aktif</p>";
    
    // WooCommerce aktif mi?
    if (!class_exists('WooCommerce')) {
        echo "<p style='color: red;'>❌ WooCommerce eklentisi aktif değil!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ WooCommerce eklentisi aktif</p>";
    
    // Superole rolü var mı?
    $roles = wp_roles()->get_names();
    if (!isset($roles['superole'])) {
        echo "<p style='color: red;'>❌ Superole rolü bulunamadı!</p>";
        return;
    }
    
    echo "<p style='color: green;'>✅ Superole rolü mevcut</p>";
    
    // Mevcut kullanıcı superole mu?
    $current_user = wp_get_current_user();
    if (!in_array('superole', $current_user->roles)) {
        echo "<p style='color: orange;'>⚠️ Mevcut kullanıcı superole rolünde değil</p>";
        echo "<p>Test etmek için bir kullanıcıyı superole rolüne atayın.</p>";
    } else {
        echo "<p style='color: green;'>✅ Mevcut kullanıcı superole rolünde</p>";
        
        // Reports menüsü eklenmiş mi test et
        global $menu;
        $reports_menu_found = false;
        foreach ($menu as $menu_item) {
            if (isset($menu_item[2]) && $menu_item[2] === 'role-custom-reports') {
                $reports_menu_found = true;
                break;
            }
        }
        
        if ($reports_menu_found) {
            echo "<p style='color: green;'>✅ Reports menüsü eklendi</p>";
        } else {
            echo "<p style='color: red;'>❌ Reports menüsü bulunamadı</p>";
        }
    }
    
    // Asset dosyaları var mı?
    $css_file = ROLE_CUSTOM_PLUGIN_DIR . 'assets/css/reports.css';
    $js_file = ROLE_CUSTOM_PLUGIN_DIR . 'assets/js/reports.js';
    
    if (file_exists($css_file)) {
        echo "<p style='color: green;'>✅ CSS dosyası mevcut: " . basename($css_file) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ CSS dosyası bulunamadı: " . $css_file . "</p>";
    }
    
    if (file_exists($js_file)) {
        echo "<p style='color: green;'>✅ JavaScript dosyası mevcut: " . basename($js_file) . "</p>";
    } else {
        echo "<p style='color: red;'>❌ JavaScript dosyası bulunamadı: " . $js_file . "</p>";
    }
    
    // AJAX endpoint'leri test et
    echo "<h3>AJAX Endpoints Test</h3>";
    
    $ajax_actions = [
        'role_custom_get_sales_data',
        'role_custom_get_revenue_data', 
        'role_custom_get_product_performance'
    ];
    
    foreach ($ajax_actions as $action) {
        if (has_action("wp_ajax_{$action}")) {
            echo "<p style='color: green;'>✅ AJAX action registered: {$action}</p>";
        } else {
            echo "<p style='color: red;'>❌ AJAX action not found: {$action}</p>";
        }
    }
    
    // Hook'lar test et
    echo "<h3>Hook'lar Test</h3>";
    
    $hooks = [
        'admin_menu' => 'add_reports_menu',
        'admin_enqueue_scripts' => 'enqueue_reports_scripts'
    ];
    
    foreach ($hooks as $hook => $function) {
        if (has_action($hook, [Role_Custom::get_instance(), $function])) {
            echo "<p style='color: green;'>✅ Hook registered: {$hook} -> {$function}</p>";
        } else {
            echo "<p style='color: red;'>❌ Hook not found: {$hook} -> {$function}</p>";
        }
    }
    
    echo "<h3>Test Tamamlandı</h3>";
    echo "<p>Reports sayfasına erişmek için: <a href='" . admin_url('admin.php?page=role-custom-reports') . "'>Raporlar</a></p>";
}

// Admin panelinde test çalıştır
if (is_admin() && current_user_can('manage_options')) {
    add_action('admin_notices', function() {
        if (isset($_GET['role_custom_test_reports'])) {
            echo '<div class="notice notice-info is-dismissible">';
            role_custom_test_reports();
            echo '</div>';
        }
    });
    
    // Test linkini admin bar'a ekle
    add_action('admin_bar_menu', function($wp_admin_bar) {
        $wp_admin_bar->add_node([
            'id' => 'role-custom-test-reports',
            'title' => 'Test Reports',
            'href' => admin_url('admin.php?role_custom_test_reports=1'),
            'meta' => ['title' => 'Role Custom Reports Test']
        ]);
    }, 100);
}
