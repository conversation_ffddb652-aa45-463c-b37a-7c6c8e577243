# Role Custom WordPress Eklentisi

## Açıklama

Role Custom, WordPress için geliştirilmiş özel bir rol yönetimi eklentisidir. Bu eklenti Tutor Instructor rolü için özel yetkilendirmeler sağlar ve admin panelinde Role Custom yönetim menüsü ekler. Tutor Instructor rolündeki kullanıcılar için Tutor LMS menülerini kısıtlar, WooCommerce'e tam erişim sağlar ve Pazarlama menüsünü gizler.

## Özellikler

### 1. Role Custom Admin Menüsü ⭐ YENİ!
Admin ve yönetici rolleri için WordPress admin sidebar'ında "Role Custom" menüsü eklenir:

**📋 Role Custom Menü Yapısı:**
- **Role Custom** (Ana menü)
  - **Eğitmenler** (Tutor Instructor rolündeki kullanıcılar listesi)
  - **Ayarlar** (Eklenti ayarları - geliştirme aşamasında)

**👥 Eğitmenler Sayfası Özellikleri:**
- Tutor Instructor rolündeki tüm kullanıcıları listeler
- İstatistik kartları (Toplam Eğitmen, Aktif Eğitmen, Toplam Kurs)
- Kullanıcı bilgileri tablosu (Ad, E-posta, Kurs Sayısı, Kayıt Tarihi)
- Kullanıcı düzenleme linkleri
- Responsive tasarım

**⚙️ Ayarlar Sayfası Özellikleri:**
- Eklenti bilgileri ve durumu
- Tutor LMS ve WooCommerce durum kontrolü
- Eklenti özelliklerinin listesi
- Gelecek özellikler hakkında bilgi

### 2. Tutor Instructor Rol Yönetimi
Eklenti artık **Tutor Instructor rolü** üzerinde çalışır (eski Superole rolü kaldırıldı):

**🔑 Tutor LMS Yetkileri (Instructor Rolünden):**
- **Temel WordPress Yetkileri**: `edit_posts`, `read`, `upload_files`
- **Yönetim Yetkileri**: `manage_tutor`, `manage_tutor_instructor`, `tutor_instructor`
- **Kurs Yetkileri**: Kurs oluşturma, düzenleme, silme, yayınlama (`edit_tutor_course`, `publish_tutor_courses`)
- **Ders Yetkileri**: Ders oluşturma, düzenleme, silme, yayınlama (`edit_tutor_lesson`, `publish_tutor_lessons`)
- **Quiz Yetkileri**: Quiz oluşturma, düzenleme, silme, yayınlama (`edit_tutor_quiz`, `publish_tutor_quizzes`)
- **Soru Yetkileri**: Soru oluşturma, düzenleme, silme, yayınlama (`edit_tutor_question`, `publish_tutor_questions`)

**📋 Erişilebilir Tutor LMS Menüleri:**
- **Kurslar** (Ana sayfa - tam kurs yönetimi)
- **Öğrenciler** (Öğrenci yönetimi)
- **Duyurular** (Kurs duyuruları)
- **Q&A** (Soru-cevap yönetimi)
- **Sınav Denemeleri** (Quiz sonuçları)
- **Tüm diğer instructor menüleri** (Araçlar ve Ayarlar hariç)

**🚫 Gizlenen Admin Menüleri:**
- Tools (Araçlar)
- Settings (Ayarlar)
- **Para Çekme Talepleri** (Withdrawals) ← **YENİ!**
- Upgrade to Pro

### 3. WooCommerce Özelleştirilmiş Menü Yapısı
Tutor Instructor rolündeki kullanıcılar için WooCommerce menü yapısı tamamen yeniden düzenlenmiştir:

**Görünür Ana Menüler:**
- **Tutor LMS** (Ana sayfa - otomatik yönlendirme) ← **YENİ!**
- **Ortamlar**
- **Ürünler** (Tüm ürün yönetimi)
- **Siparişler** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-orders`)
- **Müşteriler** ← **YENİ ANA MENÜ!** (`users.php?role=customer`)
- **Raporlar** ← **YENİ ANA MENÜ!** (`admin.php?page=wc-reports`)
- **Kuponlar** ← **YENİ ANA MENÜ!** (`edit.php?post_type=shop_coupon`)
- **Analiz** (Raporlar ve analizler)
- **Pazarlama** (Pazarlama araçları - kuponlar hariç)

**Tamamen Gizlenen Menüler:**
- **Dashboard** (Başlangıç - WordPress ana sekmesi)
- **Kullanıcılar** (WordPress ana sekmesi) ← **YENİ!**
- **WooCommerce** (Ana sekme tamamen gizli)
- **Ödemeler** (Ana sekme tamamen gizli)
- **Pazarlama** (Ana sekme tamamen gizli) ← **YENİ!**
- **Araçlar** (WordPress ana sekmesi)
- **Ayarlar** (WooCommerce alt menüsünde)
- **Durum** (WooCommerce alt menüsünde)
- **Genişletme Paketleri** (WooCommerce alt menüsünde)
- **Kuponlar** (Pazarlama alt menüsünden - artık ana menü)

### 4. Otomatik Ana Sayfa Yönlendirmesi ⭐ YENİ!
Tutor Instructor rolündeki kullanıcılar WordPress admin paneline giriş yaptığında:
- **Dashboard** (Başlangıç) sayfası yerine
- **Tutor LMS Kurslar** sayfasına otomatik yönlendirilir
- Bu sayede kullanıcılar direkt olarak kurs yönetimi ile başlar

## Gereksinimler

- WordPress 5.0 veya üzeri
- PHP 7.4 veya üzeri
- Tutor LMS eklentisi (menü kısıtlamaları için)
- WooCommerce eklentisi (e-ticaret özellikleri için)

## Kurulum

1. `role-custom` klasörünü WordPress'in `/wp-content/plugins/` dizinine yükleyin
2. WordPress admin panelinde "Eklentiler" bölümüne gidin
3. "Role Custom" eklentisini bulun ve "Etkinleştir" butonuna tıklayın
4. Eklenti etkinleştirildikten sonra "Superole" rolü otomatik olarak oluşturulacaktır

## Kullanım

### Role Custom Admin Menüsü
1. WordPress admin panelinde "Role Custom" menüsüne gidin
2. **Eğitmenler** sayfasında:
   - Tutor Instructor rolündeki tüm kullanıcıları görüntüleyin
   - İstatistikleri kontrol edin
   - Kullanıcı detaylarına erişin
3. **Ayarlar** sayfasında:
   - Eklenti durumunu kontrol edin
   - Sistem bilgilerini görüntüleyin

### Tutor Instructor Rolü Atama
1. WordPress admin panelinde "Kullanıcılar" > "Tüm Kullanıcılar" bölümüne gidin
2. Düzenlemek istediğiniz kullanıcıyı seçin
3. "Rol" dropdown menüsünden "Tutor Instructor" seçin
4. "Kullanıcıyı Güncelle" butonuna tıklayın

### Yetkilerin Kontrolü
Tutor Instructor rolüne sahip bir kullanıcı ile giriş yaparak:
- Tutor LMS menülerinin kısıtlandığını
- WooCommerce'e tam erişim olduğunu
- Sadece izin verilen menülerin görüntülendiğini kontrol edebilirsiniz

## Teknik Detaylar

### Hook'lar ve Filtreler
- `admin_menu` (priority: 999) - Tutor LMS menü kısıtlamaları
- `admin_init` - WooCommerce yetkilerinin ayarlanması
- `admin_notices` - Bildirim mesajları
- `admin_head` - CSS ile menü gizleme
- `user_has_cap` - Kullanıcı yetkilerinin filtrelenmesi

### Güvenlik
- Doğrudan dosya erişimi engellendi
- Tüm kullanıcı girişleri sanitize edildi
- WordPress nonce sistemi kullanıldı
- Yetki kontrolleri her adımda yapıldı

### Performans
- Minimum veritabanı sorgusu
- Efficient hook kullanımı
- Transient cache sistemi
- Lazy loading uygulandı

## Sorun Giderme

### Eklenti Etkinleştirme Sorunları
- WordPress ve PHP sürümlerini kontrol edin
- Diğer eklentilerle çakışma olup olmadığını kontrol edin
- WordPress debug modunu etkinleştirin

### Menü Kısıtlamaları Çalışmıyor
- Tutor LMS eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- Tarayıcı cache'ini temizleyin

### WooCommerce Yetkileri Çalışmıyor
- WooCommerce eklentisinin aktif olduğundan emin olun
- Kullanıcının Superole rolüne sahip olduğunu kontrol edin
- WordPress yetkilerini yeniden yükleyin

## Kaldırma

1. WordPress admin panelinde "Eklentiler" bölümüne gidin
2. "Role Custom" eklentisini bulun ve "Devre Dışı Bırak" butonuna tıklayın
3. Eklenti devre dışı bırakıldığında:
   - Superole rolü otomatik olarak kaldırılır
   - Bu role sahip kullanıcılar "subscriber" rolüne dönüştürülür
   - Eklenti ayarları temizlenir

## Changelog

### 1.0.0
- İlk sürüm
- Superole rolü oluşturma/kaldırma sistemi
- Tutor LMS menü kısıtlama sistemi
- WooCommerce tam erişim sistemi
- Admin bildirim sistemi
- Çoklu dil desteği hazırlığı

## Destek

Bu eklenti ile ilgili sorularınız için:
- GitHub Issues bölümünü kullanın
- WordPress.org destek forumlarına başvurun
- Geliştirici ile iletişime geçin

## Lisans

Bu eklenti GPL v2 veya üzeri lisansı altında dağıtılmaktadır.

## Geliştirici

Role Custom Developer
- Website: https://example.com
- Email: <EMAIL>

---

**Not:** Bu eklenti WordPress standartlarına uygun olarak geliştirilmiştir ve sürekli güncellenmektedir.
